<template>
  <CmpContainer full>
    <CmpCard no-fill>
      <div class="table-tree-container">
        <div class="list-tree-wrapper">
          <div class="list-tree-operator">
            <t-input v-model="filterText" placeholder="请输入关键字搜索" @change="onInput">
              <template #suffix-icon>
                <search-icon size="var(--td-comp-size-xxxs)" />
              </template>
            </t-input>
            <t-tree
              :data="treeData"
              hover
              :expand-on-click-node="true"
              :filter="filterByText"
              :load="loadEquipments"
              lazy
              activable
              expandable
              line
              value-mode="all"
              @click="onTreeNodeClick"
            >
              <template #label="{ node }">
                <div class="tree-node-content">
                  <span class="node-icon">
                    <chimney-icon v-if="node.data.type === undefined && node.data.equipmentId === undefined" />
                    <folder-icon v-else-if="node.data.type && !node.data.equipmentId" />
                    <!-- <device-icon v-else-if="node.data.equipmentId" /> -->
                  </span>
                  <span class="node-label">{{ node.label }}</span>
                  <div
                    v-if="node.data.equipmentId"
                    class="equipment-status"
                    :class="{ online: getEquipmentOnlineStatus(node.data.equipmentId) }"
                    :title="getEquipmentOnlineStatus(node.data.equipmentId) ? '在线' : '离线'"
                  >
                    <div class="status-indicator"></div>
                  </div>
                </div>
              </template>
            </t-tree>
          </div>
          <div class="list-tree-content">
            <t-row justify="space-between">
              <t-space>
                <t-button v-if="selectedEquipment" theme="primary" @click="onAddVariable">
                  <template #icon>
                    <add-icon />
                  </template>
                  新增变量
                </t-button>
                <t-button
                  v-if="selectedEquipment"
                  theme="default"
                  :disabled="!selectedRowKeys.length"
                  @click="onBatchDelete"
                >
                  <template #icon>
                    <delete-icon />
                  </template>
                  批量删除
                </t-button>
                <div v-if="tableData.length > 0" class="refresh-controls">
                  <t-space size="small" align="center">
                    <span class="refresh-label">实时刷新:</span>
                    <t-select
                      v-model="refreshInterval"
                      class="refresh-select"
                      size="small"
                      @change="onRefreshIntervalChange"
                    >
                      <t-option :value="0" label="已停止" />
                      <t-option :value="1000" label="1秒" />
                      <t-option :value="3000" label="3秒" />
                      <t-option :value="5000" label="5秒" />
                      <t-option :value="10000" label="10秒" />
                    </t-select>
                    <t-button
                      theme="default"
                      variant="outline"
                      size="small"
                      :loading="isRefreshing"
                      @click="onManualRefresh"
                    >
                      <template #icon>
                        <refresh-icon />
                      </template>
                      立即刷新
                    </t-button>
                    <t-tag v-if="refreshInterval > 0" theme="success" variant="light" size="small">
                      <!-- <circle-icon /> -->
                      运行中
                    </t-tag>
                  </t-space>
                </div>
              </t-space>
              <t-input
                v-model="searchKeyword"
                class="search-input"
                placeholder="请输入搜索关键字"
                clearable
                @enter="onSearch"
              >
                <template #suffix-icon>
                  <search-icon size="var(--td-comp-size-xxxs)" />
                </template>
              </t-input>
            </t-row>
            <t-table
              style="margin-top: 8px; font-size: 12px"
              row-key="id"
              size="small"
              :data="tableData"
              :columns="columns"
              :selected-row-keys="selectedRowKeys"
              :loading="tableLoading"
              :pagination="pagination"
              resizable
              @select-change="onSelectChange"
              @page-change="onPageChange"
            >
              <template #isUpload="slotProps">
                <t-switch
                  v-model="slotProps.row.isUpload"
                  :custom-value="[1, 0]"
                  @change="() => onSwitchChange(slotProps.row)"
                />
              </template>
              <template #op="slotProps">
                <t-space>
                  <t-link theme="primary" hover="color" @click="() => onEditVariable(slotProps.row)">编辑</t-link>
                  <t-popconfirm placement="left" theme="default" @confirm="() => onWriteData(slotProps.row)">
                    <template #icon><div></div></template>
                    <template #content>
                      <t-space direction="vertical">
                        <t-input v-model.trim="writeData" placeholder="请输入值" />
                      </t-space>
                    </template>
                    <t-link theme="primary" hover="color">写入</t-link>
                  </t-popconfirm>
                </t-space>
              </template>
            </t-table>
          </div>
        </div>
      </div>

      <!-- 变量表单对话框 -->
      <t-dialog
        v-model:visible="showDialog"
        :header="isEdit ? '编辑变量' : '新增变量'"
        :width="680"
        :footer="false"
        placement="center"
      >
        <t-form ref="formRef" :data="formData" :rules="rules" @submit="onSubmit" :label-width="115">
          <t-form-item name="varName" label="变量名称">
            <t-input v-model.trim="formData.varName" placeholder="请输入变量名称" />
          </t-form-item>
          <!-- <t-form-item name="method" label="方法">
            <t-input v-model="formData.method" placeholder="请输入方法" />
          </t-form-item> -->
          <t-form-item name="address" label="地址">
            <t-textarea v-model.trim="formData.address" placeholder="请输入地址" />
          </t-form-item>
          <t-form-item name="expressions" label="表达式">
            <t-input v-model="formData.expressions" placeholder="请输入表达式" />
          </t-form-item>
          <t-form-item name="dataType" label="数据类型">
            <t-select
              v-model="formData.dataType"
              filterable
              :options="dataTypes"
              placeholder="请选择数据类型"
              @enter="({ e }) => e.preventDefault()"
            />
          </t-form-item>
          <t-form-item name="archivePeriod" label="归档周期(毫秒)">
            <t-input type="number" v-model="formData.archivePeriod" placeholder="请输入归档周期" />
          </t-form-item>
          <t-form-item name="changeThreshold" label="变化阈值(小数)">
            <t-input type="number" v-model="formData.changeThreshold" placeholder="请输入变化阈值（如0.1）" />
          </t-form-item>
          <t-form-item name="isUpload" label="是否上传">
            <t-switch v-model="formData.isUpload" :custom-value="[1, 0]" />
          </t-form-item>
          <t-form-item name="description" label="描述">
            <t-input v-model="formData.description" placeholder="请输入描述" />
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">确定</t-button>
              <t-button theme="default" @click="showDialog = false">取消</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </t-dialog>
    </CmpCard></CmpContainer
  >
</template>

<script lang="tsx">
export default {
  name: 'EquipmentVariable',
};
</script>

<script setup lang="tsx">
import {
  AddIcon,
  ChimneyIcon,
  DeleteIcon,
  DeviceIcon,
  FolderIcon,
  SearchIcon,
  CircleIcon,
  CheckCircleFilledIcon,
  ErrorCircleFilledIcon,
  HelpCircleFilledIcon,
  RefreshIcon,
} from 'tdesign-icons-vue-next';
import {
  type FormRule,
  MessagePlugin,
  PageInfo,
  type PrimaryTableCol,
  TableRowData,
  TreeNodeModel,
} from 'tdesign-vue-next';
import { onMounted, onUnmounted, ref } from 'vue';

import { api, Services } from '@/api/system';
import { isEmpty } from 'lodash-es';

interface TreeNode {
  label: string;
  value: string;
  children?: TreeNode[];
  equipmentId?: string;
  driverCode?: string;
  loading?: boolean;
  isLeaf?: boolean;
  orgCode?: string;
  type?: string;
}

interface Variable {
  id: string;
  varName: string;
  method: string;
  address: string;
  expressions: string;
  dataType: string;
  archivePeriod: number;
  changeThreshold: number;
  isUpload: number;
  description: string;
  value?: any;
  readStatus?: 'Unknown' | 'Success' | 'Failed' | 'NotConnected' | 'InvalidAddress' | 'DataTypeError' | 'Timeout';
  errorMessage?: string;
  hasChanged?: boolean;
}

interface DriverOperationResult {
  timestamp: string;
  rawValue: any;
  processedValue: any;
  status: 'Unknown' | 'Success' | 'Failed' | 'NotConnected' | 'InvalidAddress' | 'DataTypeError' | 'Timeout';
  errorMessage: string;
  variableId: string;
}

const dataTypes = ref([]);
const filterText = ref('');
const filterByText = ref();
const treeData = ref<TreeNode[]>([]);
const selectedEquipment = ref<TreeNode | null>(null);

// 设备在线状态
const equipmentOnlineStatus = ref<Record<string, boolean>>({});
let statusRefreshTimer: NodeJS.Timeout | null = null;

// 实时刷新相关
const refreshInterval = ref(3000); // 默认3秒
const isRefreshing = ref(false);
let dataRefreshTimer: NodeJS.Timeout | null = null;
const previousValues = ref<Record<string, any>>({});
const tableData = ref<Variable[]>([]);
const tableLoading = ref(false);
const selectedRowKeys = ref<string[]>([]);
const searchKeyword = ref('');
const showDialog = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const formData = ref({
  id: '',
  varName: '',
  method: '',
  address: '',
  expressions: '',
  dataType: '',
  archivePeriod: null,
  changeThreshold: 0,
  isUpload: 0,
  description: '',
});

const rules = {
  varName: [{ required: true, message: '请输入变量名称', trigger: 'blur' }],
} as Record<string, FormRule[]>;

const pagination = ref({
  total: 0,
  pageSize: 50,
  current: 1,
  pageSizeOptions: [20, 50, 200, 500],
});

// 表格列定义
const columns: PrimaryTableCol[] = [
  { colKey: 'row-select', type: 'multiple', width: 50 },
  { colKey: 'varName', title: '变量名称', ellipsis: true, width: 180 },
  { colKey: 'address', title: '地址', ellipsis: true, width: 180 },
  { colKey: 'description', title: '描述', ellipsis: true },
  {
    colKey: 'value',
    title: '值',
    ellipsis: true,
    cell: (h, { row }) => {
      const hasChanged = row.hasChanged;
      return (
        <div class={hasChanged ? 'value-changed' : ''}>
          {row.dataType === 'AsciiString' || row.dataType === 'Utf8String' || row.dataType === 'Gb2312String' ? (
            <pre style="margin:0;">{row.value}</pre>
          ) : (
            <div style="text-align: right">{row.value}</div>
          )}
        </div>
      );
    },
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    align: 'center',
    cell: (h, { row }) => {
      const status = row.readStatus;
      const hasChanged = row.hasChanged;

      // 根据后端 OperationStatus 枚举显示状态
      switch (status) {
        case 'Success':
          return (
            <div class={`status-cell success ${hasChanged ? 'status-changed' : ''}`}>
              <check-circle-filled-icon size="14px" />
              <span>正常</span>
            </div>
          );
        case 'Failed':
          return (
            <div class={`status-cell error ${hasChanged ? 'status-changed' : ''}`}>
              <error-circle-filled-icon size="14px" />
              <span>失败</span>
            </div>
          );
        case 'NotConnected':
          return (
            <div class={`status-cell error ${hasChanged ? 'status-changed' : ''}`}>
              <error-circle-filled-icon size="14px" />
              <span>未连接</span>
            </div>
          );
        case 'InvalidAddress':
          return (
            <div class={`status-cell error ${hasChanged ? 'status-changed' : ''}`}>
              <error-circle-filled-icon size="14px" />
              <span>地址无效</span>
            </div>
          );
        case 'DataTypeError':
          return (
            <div class={`status-cell error ${hasChanged ? 'status-changed' : ''}`}>
              <error-circle-filled-icon size="14px" />
              <span>类型错误</span>
            </div>
          );
        case 'Timeout':
          return (
            <div class={`status-cell warning ${hasChanged ? 'status-changed' : ''}`}>
              <help-circle-filled-icon size="14px" />
              <span>超时</span>
            </div>
          );
        case 'Unknown':
        default:
          return (
            <div class={`status-cell unknown ${hasChanged ? 'status-changed' : ''}`}>
              <help-circle-filled-icon size="14px" />
              <span>未知</span>
            </div>
          );
      }
    },
  },
  {
    colKey: 'errorMessage',
    title: '错误信息',
    ellipsis: true,
    width: 180,
    cell: (h, { row }) => {
      if (!row.errorMessage) return '';
      const hasChanged = row.hasChanged;
      return (
        <div class={`error-message ${hasChanged ? 'error-changed' : ''}`}>
          <t-tooltip content={row.errorMessage} placement="top">
            <span class="error-text">{row.errorMessage}</span>
          </t-tooltip>
        </div>
      );
    },
  },
  {
    colKey: 'dataType',
    title: '数据类型',
    cell: (h, { row }) => {
      return <div>{dataTypes.value.find((t) => t.value == row.dataType)?.label}</div>;
    },
    ellipsis: true,
    width: 140,
  },
  { colKey: 'archivePeriod', title: '归档周期(毫秒)', width: 110, align: 'right' },
  { colKey: 'changeThreshold', title: '变化阈值', align: 'right' },
  //{ colKey: 'method', title: '方法' },
  { colKey: 'expressions', title: '表达式' },
  {
    colKey: 'isUpload',
    title: '是否上传',
    width: 65,
    align: 'center',
    fixed: 'right',
  },
  { colKey: 'op', title: '操作', width: 120, fixed: 'right' },
];

const loadDataTypes = async () => {
  const types = await api.run(Services.equipmentVariableGetDataTypes);
  dataTypes.value = types;
};

// 获取设备在线状态
const getEquipmentOnlineStatus = (equipmentId: string): boolean => {
  return equipmentOnlineStatus.value[equipmentId] || false;
};

// 刷新设备在线状态
const refreshEquipmentOnlineStatus = async () => {
  try {
    // 收集所有设备ID
    const equipmentIds: string[] = [];
    const collectEquipmentIds = (nodes: TreeNode[]) => {
      nodes.forEach((node) => {
        if (node.equipmentId) {
          equipmentIds.push(node.equipmentId);
        }
        // 只有当 children 是数组时才递归处理
        if (Array.isArray(node.children)) {
          collectEquipmentIds(node.children);
        }
      });
    };
    collectEquipmentIds(treeData.value);

    if (equipmentIds.length > 0) {
      const statusMap = await api.run(Services.equipmentGetMultiOnlineStatus, {
        equipmentIds,
      });
      equipmentOnlineStatus.value = statusMap || {};
    }
  } catch (error) {
    console.error('获取设备在线状态失败:', error);
  }
};

// 启动状态刷新定时器
const startStatusRefreshTimer = () => {
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer);
  }
  statusRefreshTimer = setInterval(refreshEquipmentOnlineStatus, 5000); // 每5秒刷新一次
};

// 停止状态刷新定时器
const stopStatusRefreshTimer = () => {
  if (statusRefreshTimer) {
    clearInterval(statusRefreshTimer);
    statusRefreshTimer = null;
  }
};

// 获取组织列表作为第一级节点
const loadTreeData = async () => {
  try {
    const orgCodes = await api.run(Services.equipmentGetOrgCodes);
    treeData.value = orgCodes.map((orgCode) => ({
      label: orgCode,
      value: `org-${orgCode}`,
      loading: false,
      isLeaf: false,
      children: true,
      orgCode,
    }));
  } catch (error) {
    MessagePlugin.error('加载组织列表失败');
  }
};

// 加载设备类型或设备列表
const loadEquipments = async (node: TreeNodeModel) => {
  if (node.data.equipmentId) return [];

  try {
    // 如果是组织节点，加载该组织下的设备类型
    if (node.data.orgCode && !node.data.type) {
      const types = await api.run(Services.equipmentGetTypes, {
        orgCode: node.data.orgCode,
      });
      return types.map((type) => ({
        label: type,
        value: `type-${type}`,
        loading: false,
        isLeaf: false,
        children: true,
        orgCode: node.data.orgCode,
        type,
      }));
    }

    // 如果是设备类型节点，加载该类型下的设备
    if (node.data.type) {
      const response = await api.run(Services.equipmentGetAll, {
        keyword: '',
        equipmentType: node.data.type,
        pageIndex: 1,
        pageSize: 999,
      });

      // 检查返回的数据结构
      if (!response || !response.list) {
        MessagePlugin.error('设备数据格式错误');
        return [];
      }

      const equipments = response.list;
      const result = equipments
        .filter((equip) => equip.orgCode === node.data.orgCode)
        .map((equip) => ({
          label: equip.equipmentName,
          value: `equipment-${equip.id}`,
          equipmentId: equip.id,
          driverCode: equip.driverCode,
          isLeaf: true,
          orgCode: equip.orgCode,
        }));

      // 加载设备后刷新在线状态
      setTimeout(refreshEquipmentOnlineStatus, 100);

      return result;
    }

    return [];
  } catch (error) {
    MessagePlugin.error('加载数据失败');
    return [];
  }
};

// 加载变量列表
const loadVariables = async () => {
  if (!selectedEquipment.value?.equipmentId) return;

  tableLoading.value = true;
  try {
    const equipmentVars = await api.run(Services.equipmentVariableGetAll, {
      equipmentId: selectedEquipment.value.equipmentId,
      keyword: searchKeyword.value,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });

    // 确保返回的数据结构完整
    const equipmentVarList = equipmentVars?.list || [];
    tableData.value = equipmentVarList.map((v) => ({
      ...v,
      isUpload: v.isUpload ?? 0,
      archivePeriod: v.archivePeriod ?? 0,
      changeThreshold: v.changeThreshold ?? 0,
      value: undefined,
      readStatus: 'Unknown' as const,
      errorMessage: '',
      hasChanged: false,
    }));

    pagination.value.total = equipmentVars?.total || 0;
  } catch (error) {
    MessagePlugin.error('加载变量数据失败');
  } finally {
    tableLoading.value = false;
  }
};

// 搜索过滤
const onInput = () => {
  filterByText.value = (node: TreeNode) => {
    return node.label.indexOf(filterText.value) >= 0;
  };
};

// 树节点点击
const onTreeNodeClick = ({ node }) => {
  const nodeData = node.data as TreeNode;

  // 停止之前的定时器
  stopDataRefreshTimer();

  if (nodeData.equipmentId) {
    selectedEquipment.value = nodeData;
    // 清空之前的值记录
    previousValues.value = {};
    loadVariables();

    // 如果设置了刷新间隔，检查设备在线状态后启动定时器
    if (refreshInterval.value > 0) {
      const isOnline = getEquipmentOnlineStatus(nodeData.equipmentId);
      if (isOnline) {
        startDataRefreshTimer(refreshInterval.value);
      } else {
        console.log('设备离线，不启动自动刷新:', nodeData.equipmentId);
      }
    }
  } else {
    selectedEquipment.value = null;
    tableData.value = [];
    previousValues.value = {};
  }
};

// 表格选择
const onSelectChange = (val: string[]) => {
  selectedRowKeys.value = val;
};

// 分页
const onPageChange = (pageInfo: PageInfo, newDataSource: TableRowData[]) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  loadVariables();
};

// 搜索
const onSearch = () => {
  pagination.value.current = 1;
  loadVariables();
};

// 新增变量弹窗
const onAddVariable = () => {
  isEdit.value = false;
  formData.value = {
    id: '',
    varName: '',
    method: '',
    address: '',
    expressions: '',
    dataType: '',
    archivePeriod: null,
    changeThreshold: 0,
    isUpload: 1,
    description: '',
  };
  showDialog.value = true;
};

// 编辑变量弹窗
const onEditVariable = (row: Variable) => {
  isEdit.value = true;
  formData.value = { ...row };
  showDialog.value = true;
};

const writeData = ref('');
const onWriteData = async (row: Variable) => {
  await api
    .run(Services.equipmentVariableWriteValue, {
      equipmentId: selectedEquipment.value?.equipmentId,
      address: row.address,
      value: writeData.value,
      dataType: row.dataType,
    })
    .then(() => {
      MessagePlugin.success('写入成功');
      writeData.value = '';
    });
};

// 刷新间隔变化处理
const onRefreshIntervalChange = (interval: number) => {
  stopDataRefreshTimer();
  if (interval > 0 && selectedEquipment.value?.equipmentId) {
    // 检查设备是否在线再启动定时器
    const isOnline = getEquipmentOnlineStatus(selectedEquipment.value.equipmentId);
    if (isOnline) {
      startDataRefreshTimer(interval);
    } else {
      MessagePlugin.warning('设备离线，无法启动自动刷新');
    }
  }
};

// 手动刷新
const onManualRefresh = async () => {
  if (!selectedEquipment.value?.equipmentId) return;

  // 检查设备是否在线
  const isOnline = getEquipmentOnlineStatus(selectedEquipment.value.equipmentId);
  if (!isOnline) {
    MessagePlugin.warning('设备离线，无法刷新数据');
    return;
  }

  await refreshVariableValues();
};

// 启动数据刷新定时器
const startDataRefreshTimer = (interval: number) => {
  if (dataRefreshTimer) {
    clearInterval(dataRefreshTimer);
  }
  dataRefreshTimer = setInterval(() => {
    refreshVariableValues();
  }, interval);
};

// 停止数据刷新定时器
const stopDataRefreshTimer = () => {
  if (dataRefreshTimer) {
    clearInterval(dataRefreshTimer);
    dataRefreshTimer = null;
  }
};

// 刷新变量值
const refreshVariableValues = async () => {
  if (!selectedEquipment.value?.equipmentId || isRefreshing.value) return;

  // 检查设备是否在线，只有在线设备才进行数据刷新
  const isOnline = getEquipmentOnlineStatus(selectedEquipment.value.equipmentId);
  if (!isOnline) {
    console.log('设备离线，跳过数据刷新:', selectedEquipment.value.equipmentId);
    // 将所有变量状态设为设备未连接
    tableData.value.forEach((item) => {
      if (item.readStatus !== 'NotConnected') {
        item.readStatus = 'NotConnected';
        item.errorMessage = '设备离线';
        item.hasChanged = true;
        // 延迟移除动画类
        setTimeout(() => {
          item.hasChanged = false;
        }, 600);
      }
    });
    return;
  }

  isRefreshing.value = true;
  try {
    // 调用新的带状态信息的API
    const data: Record<string, DriverOperationResult> = await api.run(
      Services.equipmentVariableGetRealTimeValuesWithStatus,
      {
        equipmentId: selectedEquipment.value.equipmentId,
      },
    );

    if (isEmpty(data)) {
      // 如果没有数据，将所有变量状态设为未知
      tableData.value.forEach((item) => {
        item.readStatus = 'Unknown';
        item.errorMessage = '无数据';
        item.hasChanged = false;
      });
      return;
    }

    // 更新表格数据并检查变化
    tableData.value.forEach((item) => {
      const driverResult = data[item.varName];

      if (driverResult) {
        const newValue = driverResult.rawValue;
        const oldValue = previousValues.value[item.varName];

        // 检查值、状态或错误信息是否发生变化
        const valueChanged = newValue !== undefined && newValue !== oldValue;
        const statusChanged = item.readStatus !== driverResult.status;
        const errorChanged = item.errorMessage !== driverResult.errorMessage;
        const hasChanged = valueChanged || statusChanged || errorChanged;

        // 更新变量信息
        item.value = newValue;
        item.readStatus = driverResult.status;
        item.errorMessage = driverResult.errorMessage || '';
        item.hasChanged = hasChanged;

        // 保存当前值作为下次比较的基准
        if (newValue !== undefined) {
          previousValues.value[item.varName] = newValue;
        }

        // 如果有变化，添加动画效果
        if (hasChanged) {
          // 延迟移除动画类，让放大效果显示后还原
          setTimeout(() => {
            item.hasChanged = false;
          }, 600);
        }
      } else {
        // 如果没有找到对应的状态信息，设为未知状态
        item.readStatus = 'Unknown';
        item.errorMessage = '未找到变量状态信息';
        item.hasChanged = false;
      }
    });
  } catch (error) {
    console.error('刷新变量值失败:', error);
    // 更新所有变量状态为错误
    tableData.value.forEach((item) => {
      item.readStatus = 'Failed';
      item.errorMessage = '通信异常';
      item.hasChanged = false;
    });
  } finally {
    isRefreshing.value = false;
  }
};

// 提交表单（新增或更新）
const onSubmit = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    try {
      if (isEdit.value) {
        // 编辑模式
        await api.run(Services.equipmentVariableUpdate, {
          ...formData.value,
        });
        MessagePlugin.success('更新变量成功');
      } else {
        // 新增模式
        await api.run(Services.equipmentVariableAdd, {
          ...formData.value,
          equipmentId: selectedEquipment.value?.equipmentId,
        });
        MessagePlugin.success('新增变量成功');
      }
      showDialog.value = false;
      loadVariables();
    } catch (error) {
      MessagePlugin.error(isEdit.value ? '更新变量失败' : '新增变量失败');
    }
  } else {
    MessagePlugin.warning(firstError);
  }
};

// 开关状态变化
const onSwitchChange = async (row: Variable) => {
  try {
    await api.run(Services.equipmentVariableUpdate, {
      ...row,
    });
    MessagePlugin.success('更新成功');
  } catch (error) {
    // 如果更新失败，回滚UI状态
    row.isUpload = row.isUpload === 1 ? 0 : 1;
    MessagePlugin.error('更新失败');
  }
};

// 批量删除
const onBatchDelete = async () => {
  try {
    await api.run(Services.equipmentVariableBatchDelete, { ids: selectedRowKeys.value });
    MessagePlugin.success('批量删除成功');
    selectedRowKeys.value = [];
    loadVariables();
  } catch (error) {
    MessagePlugin.error('批量删除失败');
  }
};

onMounted(() => {
  loadTreeData();
  loadDataTypes();
  startStatusRefreshTimer();
});

onUnmounted(() => {
  stopStatusRefreshTimer();
  stopDataRefreshTimer();
});
</script>

<style lang="less" scoped>
.table-tree-container {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .t-tree {
    margin-top: var(--td-comp-margin-xxl);
  }
}

.list-tree-wrapper {
  overflow-y: hidden;
}

.list-tree-operator {
  width: 280px;
  float: left;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.list-tree-content {
  border-left: 1px solid var(--td-border-level-1-color);
  overflow: auto;
  padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl);
}

.search-input {
  width: 240px;
}

/* 树节点内容样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;

  .node-icon {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .node-label {
    flex: 1;
    line-height: 1.4;
  }
}

/* 设备状态样式 */
.equipment-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--td-error-color);
    animation: pulse-offline 2s infinite;
  }

  &.online {
    .status-indicator {
      background-color: var(--td-success-color);
      animation: pulse-online 2s infinite;
    }
  }
}

@keyframes pulse-online {
  0% {
    box-shadow: 0 0 0 0 rgba(22, 163, 74, 0.6);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(22, 163, 74, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(22, 163, 74, 0);
  }
}

@keyframes pulse-offline {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.6);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* 刷新控件样式 */
.refresh-controls {
  padding: 4px 12px;
  background: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-medium);

  .refresh-label {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    font-weight: 500;
  }

  .refresh-select {
    width: 80px;
  }
}

/* 状态单元格样式 */
.status-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;

  &.success {
    color: var(--td-success-color);
  }

  &.error {
    color: var(--td-error-color);
  }

  &.warning {
    color: var(--td-warning-color);
  }

  &.unknown {
    color: var(--td-gray-color-6);
  }
}

/* 错误信息样式 */
.error-message {
  .error-text {
    color: var(--td-error-color);
    font-size: 12px;
    cursor: help;
    text-decoration: underline;
    text-decoration-style: dotted;
  }
}

/* 值变化动画效果 */
.value-changed {
  background: linear-gradient(135deg, var(--td-success-color-1), var(--td-success-color-2));
  border-radius: 4px;
  padding: 2px 6px;
  transform: scale(1.05);
  transition: all 0.6s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 状态变化动画效果 */
.status-changed {
  background: linear-gradient(135deg, var(--td-warning-color-1), var(--td-warning-color-2));
  border-radius: 4px;
  padding: 2px 6px;
  transform: scale(1.05);
  transition: all 0.6s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 错误信息变化动画效果 */
.error-changed {
  background: linear-gradient(135deg, var(--td-error-color-1), var(--td-error-color-2));
  border-radius: 4px;
  padding: 2px 6px;
  transform: scale(1.05);
  transition: all 0.6s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
